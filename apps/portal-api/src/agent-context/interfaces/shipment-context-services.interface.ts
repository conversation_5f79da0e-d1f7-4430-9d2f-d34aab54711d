import {
  Shipment,
  ValidateShipmentComplianceResponseDto,
  Organization,
  Email,
  CommercialInvoice,
  Importer
} from "nest-modules";
import { QueryRunner } from "typeorm";

// ===== SERVICE INTERFACES FOR _SERVICES PROPERTY =====

/**
 * Interface for EmailService methods used in intent handlers
 */
export interface IEmailService {
  /**
   * Sends an alert email to the backoffice address
   */
  sendBackofficeAlert(
    subject: string,
    body: string,
    organization: number | Organization,
    queryRunner?: QueryRunner
  ): Promise<Email | null>;
}

/**
 * Interface for RNSStatusChangeEmailSender methods used in intent handlers
 */
export interface IRNSStatusChangeEmailSender {
  /**
   * Creates a CAD attachment object for email
   */
  createCADAttachment(
    shipment: Shipment,
    commercialInvoices: Array<CommercialInvoice>,
    organizationImporter: Importer,
    queryRunner?: QueryRunner
  ): Promise<{ fileName: string; mimeType: string; b64Data: string }>;
}

/**
 * Interface for RnsProofService methods used in intent handlers
 */
export interface IRnsProofService {
  /**
   * Gets RNS proof of release data for a shipment
   */
  getRNSProofOfRelease(shipment: Shipment): Promise<{
    isReleased: boolean;
    rnsResponse: unknown | null; // Using unknown for CandataRNSResponseDto to avoid circular deps
    releaseDate: string | null;
  }>;
}

/**
 * Interface for ImporterService methods used in intent handlers
 */
export interface IImporterService {
  /**
   * Gets importers for an organization
   */
  getImporters(
    params: {
      organizationId: number;
      limit?: number;
    },
    queryRunner?: QueryRunner
  ): Promise<{
    importers: Importer[];
    total: number;
    skip: number;
    limit: number;
  }>;
}

/**
 * Interface for CustomsStatusListener methods used in intent handlers
 */
export interface ICustomsStatusListener {
  // Add methods as needed when they are used in intent handlers
}

/**
 * Interface for EntrySubmissionService methods used in intent handlers
 */
export interface IEntrySubmissionService {
  /**
   * Attempts to submit a shipment for customs processing following established workflow.
   * Handles validation, timing checks, and actual submission with proper transaction management.
   *
   * This method encapsulates the complex submission logic that was previously duplicated
   * across handlers, providing a clean abstraction for submission workflows.
   *
   * @param shipment The shipment to submit
   * @param compliance Pre-validated compliance data from agent-context
   * @param organizationId Organization ID for service resolution
   * @returns Promise resolving to submission results or error information
   */
  attemptShipmentSubmission(
    shipment: Shipment,
    compliance: ValidateShipmentComplianceResponseDto,
    organizationId: number
  ): Promise<{
    submissionResult?: {
      liveShipmentId: number | null;
      liveEntryUploadFailedShipment: { shipmentId: number; failedReason: string } | null;
      customsStatusCheckErrorShipment: any | null;
      shipmentStatusUpdate: { shipmentId: number; newStatus: string } | null;
    };
    submissionError?: string;
  }>;
}

/**
 * Interface for CommercialInvoiceService methods used in intent handlers
 */
export interface ICommercialInvoiceService {
  // Add methods as needed when they are used in intent handlers
}

// ===== CONTEXT BUILDING INTERFACES =====

/**
 * Interface for fetching shipment-related data
 */
export interface IShipmentDataProvider {
  /**
   * Fetches organization data
   */
  fetchOrganization(organizationId: number, queryRunner?: QueryRunner): Promise<Organization>;

  /**
   * Fetches shipment data
   */
  fetchShipment(shipmentId: number, queryRunner?: QueryRunner): Promise<Shipment>;

  /**
   * Fetches compliance data for a shipment
   */
  fetchCompliance(
    shipmentId: number,
    queryRunner?: QueryRunner
  ): Promise<ValidateShipmentComplianceResponseDto>;
}

/**
 * Interface for evaluating business rules
 */
export interface IBusinessRuleEvaluator {
  /**
   * Determines if a shipment can be rushed
   */
  canShipmentBeRushed(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): boolean;

  /**
   * Determines if CAD document can be generated
   */
  canGenerateCAD(customsStatus: string): boolean;

  /**
   * Determines if RNS proof can be generated
   */
  canGenerateRNSProof(customsStatus: string): boolean;

  /**
   * Determines if shipment is compliant and ready to submit
   */
  isCompliant(compliance: ValidateShipmentComplianceResponseDto): boolean;

  /**
   * Determines if shipment is released
   */
  isReleased(customsStatus: string): boolean;

  /**
   * Determines if shipment has been submitted
   */
  isSubmitted(shipment: Shipment): boolean;

  /**
   * Determines if shipment can be modified
   */
  canBeModified(shipment: Shipment): boolean;

  /**
   * Determines if entry has been uploaded
   */
  isEntryUploaded(shipment: Shipment): Promise<boolean>;

  /**
   * Determines if entry can be updated
   */
  canUpdateEntry(shipment: Shipment): Promise<boolean>;

  /**
   * Determines if all documents have been received
   */
  isAllDocsReceived(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): boolean;

  /**
   * Determines if the shipment has all required documents based on customs status
   */
  determineDocumentCompleteness(shipment: Shipment): boolean;

  /**
   * Gets the reason why a shipment cannot be rushed
   */
  getRushBlockingReason(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): string;

  /**
   * Gets the reason why CAD cannot be generated
   */
  getCADBlockingReason(shipment: Shipment): string;

  /**
   * Gets the reason why RNS proof cannot be generated
   */
  getRNSBlockingReason(shipment: Shipment): string;
}

/**
 * Interface for formatting context data for display
 */
export interface IContextFormatter {
  /**
   * Formats customs status for display
   */
  formatCustomsStatus(customsStatus: string): string;

  /**
   * Builds shipment identifiers object
   */
  buildShipmentIdentifiers(shipment: Shipment): {
    hblNumber: string | null;
    cargoControlNumber: string | null;
    transactionNumber: string | null;
    containerNumbers: string[];
    formattedContainers: string;
    hasMultipleContainers: boolean;
    primaryContainer: string | null;
  };

  /**
   * Builds ETA information object
   */
  buildEtaInformation(shipment: Shipment): {
    etaPortValue?: string;
    portName?: string;
    etaDestinationValue?: string;
    destinationName?: string;
  };

  /**
   * Builds shipping information object
   */
  buildShippingInformation(shipment: Shipment): {
    isTrackerOnline: boolean;
    shipmentStatus: string;
    isAir: boolean;
    trackingStatus?: string;
  };

  /**
   * Builds document data status
   */
  buildDocumentDataStatus(
    shipment: Shipment,
    compliance: ValidateShipmentComplianceResponseDto
  ): {
    hasHBLDataForSubmission: boolean;
    hasAnEmfDataForSubmission: boolean;
    hasCompleteHBLData: boolean;
    hasCompleteAnEmfData: boolean;
    ciReceived: boolean;
    plReceived: boolean;
    hblStatus: "Received" | "Missing";
    anEmfStatus: "Received" | "Missing";
    ciPlStatus: "Received" | "Missing";
  };

  /**
   * Builds missing fields analysis
   */
  buildMissingFieldsAnalysis(compliance: ValidateShipmentComplianceResponseDto): {
    missingIdentifiers: string[];
    missingMeasurements: string[];
    missingTiming: string[];
    missingLocations: string[];
    ogdFilingStatus: "pending" | "complete" | "not-required";
    formattedMissingFields: string[];
  };

  /**
   * Builds template context
   */
  buildTemplateContext(
    shipment: Shipment,
    compliance: ValidateShipmentComplianceResponseDto
  ): {
    identifiers: {
      ccn: string;
      hbl: string;
      containers: string[];
      formattedContainers: string;
    };
    documentStatus: {
      hblStatus: "Received" | "Missing";
      anEmfStatus: "Received" | "Missing";
      ciPlStatus: "Received" | "Missing";
    };
    missingFields: {
      forPendingCommercialInvoice: string[];
      forPendingConfirmation: string[];
      formatted: string[];
    };
    timing: {
      etaPort: string | null;
      etaDestination: string | null;
      releaseDate: string | null;
      formattedEtaPort: string;
      formattedEtaDestination: string;
      formattedReleaseDate: string;
    };
    statusContext: {
      primaryMessage: string;
      secondaryDetails: string[];
      actionRequired: boolean;
    };
  };

  /**
   * Builds document receipt status
   */
  buildDocumentReceiptStatus(
    shipment: Shipment,
    compliance: ValidateShipmentComplianceResponseDto
  ): {
    hblReceived: boolean;
    anEmfReceived: boolean;
    ciReceived: boolean;
    plReceived: boolean;
  };

  /**
   * Builds missing fields status
   */
  buildMissingFieldsStatus(compliance: ValidateShipmentComplianceResponseDto): string[];

  /**
   * Formats compliance response to strings
   */
  formatComplianceErrors(compliance: ValidateShipmentComplianceResponseDto): string[];
}
