import { Injectable, Logger, NotFoundException, BadRequestException, Inject } from "@nestjs/common";
import { QueryRunner } from "typeorm";
import { ShipmentContext } from "../types/shipment-context.types";
import { SafeEvaluationUtil } from "../utils/safe-evaluation.util";
import {
  IShipmentDataProvider,
  IBusinessRuleEvaluator,
  IContextFormatter,
  IEmailService,
  IRNSStatusChangeEmailSender,
  IRnsProofService,
  IImporterService,
  ICustomsStatusListener,
  IEntrySubmissionService,
  ICommercialInvoiceService
} from "../interfaces/shipment-context-services.interface";
import {
  STATUS_RESPONSE_MESSAGES,
  RUSH_ACTION_MESSAGES,
  DOCUMENT_BLOCKER_REASONS
} from "../../core-agent/constants/response-messages.constants";
import { Shipment, ValidateShipmentComplianceResponseDto, ShipmentColumn } from "nest-modules";
import moment from "moment";

/**
 * Extended context interface for internal use that includes service instances.
 * This allows us to keep the main ShipmentContext interface clean while still
 * providing service access where needed.
 */
export interface ShipmentContextWithServices extends ShipmentContext {
  _services: {
    emailService?: IEmailService;
    rnsStatusChangeEmailSender?: IRNSStatusChangeEmailSender;
    rnsProofService?: IRnsProofService;
    customsStatusListener?: ICustomsStatusListener;
    entrySubmissionService?: IEntrySubmissionService;
    importerService?: IImporterService;
    commercialInvoiceService?: ICommercialInvoiceService;
  };
}

/**
 * Simplified, focused ShipmentContextService that orchestrates context building
 * using three core interfaces. All business logic is delegated to the interfaces.
 */
@Injectable()
export class ShipmentContextService {
  private readonly logger = new Logger(ShipmentContextService.name);

  constructor(
    @Inject("SHIPMENT_DATA_PROVIDER") private readonly dataProvider: IShipmentDataProvider,
    @Inject("BUSINESS_RULE_EVALUATOR") private readonly ruleEvaluator: IBusinessRuleEvaluator,
    @Inject("CONTEXT_FORMATTER") private readonly formatter: IContextFormatter,
    @Inject("ENTRY_SUBMISSION_SERVICE") private readonly entrySubmissionService: IEntrySubmissionService,
    private readonly safeEvaluator: SafeEvaluationUtil
  ) {}

  /**
   * Builds comprehensive context for a shipment.
   *
   * @param shipmentId - The ID of the shipment to build context for
   * @param organizationId - The organization ID for scoping services
   * @param queryRunner - Optional queryRunner for transaction consistency
   * @returns Promise resolving to comprehensive ShipmentContext
   */
  async buildContext(
    shipmentId: number,
    organizationId: number,
    queryRunner?: QueryRunner
  ): Promise<ShipmentContextWithServices> {
    this.logger.log(`Building context for shipment ${shipmentId}, organization ${organizationId}`);

    // Validate input parameters
    if (!shipmentId || shipmentId <= 0) {
      throw new BadRequestException(`Invalid shipmentId: ${shipmentId}`);
    }
    if (!organizationId || organizationId <= 0) {
      throw new BadRequestException(`Invalid organizationId: ${organizationId}`);
    }

    try {
      // Fetch base data
      const organization = await this.dataProvider.fetchOrganization(organizationId, queryRunner);
      const shipment = await this.dataProvider.fetchShipment(shipmentId, queryRunner);
      const compliance = await this.dataProvider.fetchCompliance(shipmentId, queryRunner);

      // Build comprehensive context using safe evaluation
      return {
        // Raw data
        shipment,
        compliance,
        organization,

        // Business rule evaluations (all delegated to rule evaluator with safe evaluation)
        canRush: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.canShipmentBeRushed(shipment, compliance),
          false,
          "canRush"
        ),
        canGenerateCAD: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.canGenerateCAD(shipment.customsStatus),
          false,
          "canGenerateCAD"
        ),
        canGenerateRNSProof: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.canGenerateRNSProof(shipment.customsStatus),
          false,
          "canGenerateRNSProof"
        ),
        isCompliant: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.isCompliant(compliance),
          false,
          "isCompliant"
        ),
        isReleased: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.isReleased(shipment.customsStatus),
          false,
          "isReleased"
        ),
        isSubmitted: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.isSubmitted(shipment),
          false,
          "isSubmitted"
        ),
        canBeModified: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.canBeModified(shipment),
          true,
          "canBeModified"
        ),
        isEntryUploaded: await this.safeEvaluator.evaluateAsync(
          () => this.ruleEvaluator.isEntryUploaded(shipment),
          false,
          "isEntryUploaded"
        ),
        canUpdateEntry: await this.safeEvaluator.evaluateAsync(
          () => this.ruleEvaluator.canUpdateEntry(shipment),
          false,
          "canUpdateEntry"
        ),
        isAllDocsReceived: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.isAllDocsReceived(shipment, compliance),
          false,
          "isAllDocsReceived"
        ),

        // Blocking reasons (delegated with safe evaluation)
        rushBlockingReason: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.getRushBlockingReason(shipment, compliance),
          "Unable to determine blocking reason",
          "rushBlockingReason"
        ),
        cadBlockingReason: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.getCADBlockingReason(shipment),
          "",
          "cadBlockingReason"
        ),
        rnsBlockingReason: this.safeEvaluator.evaluate(
          () => this.ruleEvaluator.getRNSBlockingReason(shipment),
          "Proof of release is only available once the goods have been released by customs.",
          "rnsBlockingReason"
        ),

        // Detailed data for templates (delegated with safe evaluation)
        missingDocuments: this.safeEvaluator.evaluate(
          () => compliance.missingFields || [],
          [],
          "missingDocuments"
        ),
        complianceErrors: this.safeEvaluator.evaluate(
          () => this.formatter.formatComplianceErrors(compliance) || [],
          [],
          "complianceErrors"
        ),
        nonCompliantInvoices: this.safeEvaluator.evaluate(
          () => compliance.nonCompliantInvoices || [],
          [],
          "nonCompliantInvoices"
        ),

        // Formatted display values (all delegated to formatter with safe evaluation)
        formattedCustomsStatus: this.safeEvaluator.evaluate(
          () => this.formatter.formatCustomsStatus(shipment.customsStatus),
          "Status Unknown",
          "formattedCustomsStatus"
        ),
        shipmentIdentifiers: this.safeEvaluator.evaluate(
          () => this.formatter.buildShipmentIdentifiers(shipment),
          this.getEmptyShipmentIdentifiers(),
          "shipmentIdentifiers"
        ),
        etaInformation: this.safeEvaluator.evaluate(
          () => this.formatter.buildEtaInformation(shipment),
          this.getEmptyEtaInformation(),
          "etaInformation"
        ),
        shippingInformation: this.safeEvaluator.evaluate(
          () => this.formatter.buildShippingInformation(shipment),
          this.getEmptyShippingInformation(),
          "shippingInformation"
        ),
        documentDataStatus: this.safeEvaluator.evaluate(
          () => this.formatter.buildDocumentDataStatus(shipment, compliance),
          this.getEmptyDocumentDataStatus(),
          "documentDataStatus"
        ),
        missingFieldsAnalysis: this.safeEvaluator.evaluate(
          () => this.formatter.buildMissingFieldsAnalysis(compliance),
          this.getEmptyMissingFieldsAnalysis(),
          "missingFieldsAnalysis"
        ),
        templateContext: this.safeEvaluator.evaluate(
          () => this.formatter.buildTemplateContext(shipment, compliance),
          this.getEmptyTemplateContext(),
          "templateContext"
        ),
        smartTemplateContext: this.safeEvaluator.evaluate(
          () => this.buildSmartTemplateContext(shipment, compliance),
          this.getEmptySmartTemplateContext(),
          "smartTemplateContext"
        ),
        documentReceiptStatus: this.safeEvaluator.evaluate(
          () => this.formatter.buildDocumentReceiptStatus(shipment, compliance),
          this.getEmptyDocumentReceiptStatus(),
          "documentReceiptStatus"
        ),
        missingFieldsStatus: this.safeEvaluator.evaluate(
          () => this.formatter.buildMissingFieldsStatus(compliance),
          [],
          "missingFieldsStatus"
        ),

        // User interaction flags (set by intent handlers)
        directlyAsked: {},

        // Side effect results (populated by intent handlers)
        sideEffects: {
          cadDocument: null,
          rnsProofData: null,
          backofficeAlerts: {}
        },

        // Service instances (Note: These will need to be provided by the adapter)
        _services: {
          // Services will be populated by the adapter as needed
          // All services are optional to prevent null pointer exceptions
          entrySubmissionService: this.entrySubmissionService
        }
      };
    } catch (error) {
      this.logger.error(`Failed to build context for shipment ${shipmentId}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException(
        `Unable to retrieve shipment information for shipment ${shipmentId}: ${error.message}`
      );
    }
  }

  /**
   * Refreshes the shipment context to get the latest data from the database.
   * This is critical after operations that modify shipment data (like submission).
   */
  async refreshShipmentInContext(
    context: ShipmentContextWithServices,
    queryRunner?: QueryRunner
  ): Promise<void> {
    if (!context.shipment?.id || !context.organization?.id) {
      this.logger.warn("Cannot refresh context: missing shipment ID or organization ID");
      return;
    }

    try {
      const oldStatus = context.shipment.customsStatus;

      // Fetch fresh shipment data
      const refreshedShipment = await this.dataProvider.fetchShipment(context.shipment.id, queryRunner);
      context.shipment = refreshedShipment;

      // Update business rule evaluations that depend on shipment status
      context.canGenerateCAD = this.safeEvaluator.evaluate(
        () => this.ruleEvaluator.canGenerateCAD(refreshedShipment.customsStatus),
        false,
        "canGenerateCAD"
      );

      context.canGenerateRNSProof = this.safeEvaluator.evaluate(
        () => this.ruleEvaluator.canGenerateRNSProof(refreshedShipment.customsStatus),
        false,
        "canGenerateRNSProof"
      );

      context.isReleased = this.safeEvaluator.evaluate(
        () => this.ruleEvaluator.isReleased(refreshedShipment.customsStatus),
        false,
        "isReleased"
      );

      context.isSubmitted = this.safeEvaluator.evaluate(
        () => this.ruleEvaluator.isSubmitted(refreshedShipment),
        false,
        "isSubmitted"
      );

      // Update formatted display values
      context.formattedCustomsStatus = this.safeEvaluator.evaluate(
        () => this.formatter.formatCustomsStatus(refreshedShipment.customsStatus),
        "Status Unknown",
        "formattedCustomsStatus"
      );

      // Update status-dependent fields in smart template context
      if (context.smartTemplateContext) {
        context.smartTemplateContext.cadDocumentAvailable = context.canGenerateCAD;
        context.smartTemplateContext.rnsDocumentAvailable = context.canGenerateRNSProof;
        context.smartTemplateContext.statusResponseMessage =
          STATUS_RESPONSE_MESSAGES[refreshedShipment.customsStatus] || "Status unknown";
        context.smartTemplateContext.formattedReleaseDate = refreshedShipment.releaseDate
          ? this.formatEtaDate(refreshedShipment.releaseDate)
          : undefined;
        context.smartTemplateContext.transactionNumber = refreshedShipment.transactionNumber;
      }

      this.logger.log(
        `🔄 CONTEXT REFRESH: Refreshed shipment ${context.shipment.id} ` +
          `status: ${oldStatus} -> ${refreshedShipment.customsStatus} ` +
          `(changed: ${oldStatus !== refreshedShipment.customsStatus})`
      );
    } catch (error) {
      this.logger.error(
        `Failed to refresh context for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      // Don't throw - we can continue with the stale context
    }
  }

  /**
   * Inject services into the context for intent handlers.
   * This is typically called by higher-level orchestrating services that have access to
   * REQUEST-scoped services.
   */
  injectServices(
    context: ShipmentContextWithServices,
    services: Partial<ShipmentContextWithServices["_services"]>
  ): void {
    Object.assign(context._services, services);
    this.logger.debug(`Injected services into context for shipment ${context.shipment.id}`);
  }

  // ===== FALLBACK METHODS =====

  private getEmptyShipmentIdentifiers() {
    return {
      hblNumber: null,
      cargoControlNumber: null,
      transactionNumber: null,
      containerNumbers: [],
      formattedContainers: "",
      hasMultipleContainers: false,
      primaryContainer: null
    };
  }

  private getEmptyShippingInformation() {
    return {
      isTrackerOnline: false,
      shipmentStatus: "Unknown",
      isAir: false,
      trackingStatus: undefined
    };
  }

  private getEmptyEtaInformation() {
    return {
      etaPortValue: undefined,
      portName: undefined,
      etaDestinationValue: undefined,
      destinationName: undefined
    };
  }

  private getEmptyDocumentReceiptStatus() {
    return {
      hblReceived: false,
      anEmfReceived: false,
      ciReceived: false,
      plReceived: false
    };
  }

  private getEmptyDocumentDataStatus() {
    return {
      hasHBLDataForSubmission: false,
      hasAnEmfDataForSubmission: false,
      hasCompleteHBLData: false,
      hasCompleteAnEmfData: false,
      ciReceived: false,
      plReceived: false,
      hblStatus: "Missing" as const,
      anEmfStatus: "Missing" as const,
      ciPlStatus: "Missing" as const
    };
  }

  private getEmptyMissingFieldsAnalysis() {
    return {
      missingIdentifiers: [],
      missingMeasurements: [],
      missingTiming: [],
      missingLocations: [],
      ogdFilingStatus: "not-required" as const,
      formattedMissingFields: []
    };
  }

  private getEmptyTemplateContext() {
    return {
      identifiers: {
        ccn: "",
        hbl: "",
        containers: [],
        formattedContainers: ""
      },
      documentStatus: {
        hblStatus: "Missing" as const,
        anEmfStatus: "Missing" as const,
        ciPlStatus: "Missing" as const
      },
      missingFields: {
        forPendingCommercialInvoice: [],
        forPendingConfirmation: [],
        formatted: []
      },
      timing: {
        etaPort: null,
        etaDestination: null,
        releaseDate: null,
        formattedEtaPort: "TBD",
        formattedEtaDestination: "TBD",
        formattedReleaseDate: "TBD"
      },
      statusContext: {
        primaryMessage: "Status unknown",
        secondaryDetails: [],
        actionRequired: false
      }
    };
  }

  /**
   * Builds smart template context with status-aware messages and document availability
   */
  private buildSmartTemplateContext(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto) {
    return {
      statusResponseMessage: STATUS_RESPONSE_MESSAGES[shipment.customsStatus] || "Status unknown",
      rushActionMessage: RUSH_ACTION_MESSAGES[shipment.customsStatus],
      documentBlockerReason: DOCUMENT_BLOCKER_REASONS[shipment.customsStatus],
      transportMode: this.getTransportMode(shipment.modeOfTransport),
      hasETA: !!shipment.etaPort,
      etaDate: shipment.etaPort ? this.formatEtaDate(shipment.etaPort) : undefined,
      formattedReleaseDate: shipment.releaseDate ? this.formatEtaDate(shipment.releaseDate) : undefined,
      transactionNumber: shipment.transactionNumber,
      cadDocumentAvailable: this.ruleEvaluator.canGenerateCAD(shipment.customsStatus),
      rnsDocumentAvailable: this.ruleEvaluator.canGenerateRNSProof(shipment.customsStatus),
      documentStatus: this.buildDocumentStatus(compliance),
      missingItems: this.buildMissingItemsList(compliance),
      ogdFilingStatus: compliance?.missingFields?.length === 0 ? "Completed" : "Pending"
    };
  }

  private getTransportMode(mode: string): "OCEAN" | "AIR" | "TRUCK" {
    if (mode?.toLowerCase().includes("ocean")) return "OCEAN";
    if (mode?.toLowerCase().includes("air")) return "AIR";
    return "TRUCK";
  }

  private formatEtaDate(etaDate: Date): string {
    return moment(etaDate).format("MMMM DD, YYYY");
  }

  private buildDocumentStatus(compliance: ValidateShipmentComplianceResponseDto): {
    hbl: "Received" | "Missing";
    anEmf: "Received" | "Missing";
    ciPl: "Received" | "Missing";
  } {
    return {
      hbl: compliance?.missingFields?.includes(ShipmentColumn.hblNumber) ? "Missing" : "Received",
      anEmf: compliance?.missingFields?.some((field) => field.includes("arrival") || field.includes("emf"))
        ? "Missing"
        : "Received",
      ciPl: compliance?.noCommercialInvoice ? "Missing" : "Received"
    };
  }

  private buildMissingItemsList(compliance: ValidateShipmentComplianceResponseDto): string[] {
    const missing = [];
    if (compliance?.missingFields?.includes(ShipmentColumn.cargoControlNumber)) missing.push("CCN");
    if (compliance?.missingFields?.includes(ShipmentColumn.weight)) missing.push("Weight");
    return missing;
  }

  private getEmptySmartTemplateContext() {
    return {
      statusResponseMessage: "Status unknown",
      rushActionMessage: undefined,
      documentBlockerReason: undefined,
      transportMode: "OCEAN" as const,
      hasETA: false,
      etaDate: undefined,
      formattedReleaseDate: undefined,
      transactionNumber: undefined,
      cadDocumentAvailable: false,
      rnsDocumentAvailable: false,
      documentStatus: {
        hbl: "Missing" as const,
        anEmf: "Missing" as const,
        ciPl: "Missing" as const
      },
      missingItems: [],
      ogdFilingStatus: "Pending"
    };
  }
}
