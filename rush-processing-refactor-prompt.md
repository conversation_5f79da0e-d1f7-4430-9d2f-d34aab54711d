# Rush Processing Handler Refactoring Plan

## Current Implementation Analysis

The `request-rush-processing.handler.ts` was recently updated to include validation and submission logic, but this implementation has several architectural concerns:

### Current Dependencies (Problematic)
```typescript
import { EmailService } from "../../email/services/email.service";
import { ShipmentResponseService } from "../services/shipment-response.service";
import { CustomStatusService } from "../../shipment/services/custom-status.service";
import { ComplianceValidationService } from "../../shipment/services/compliance-validation.service";
import { DataSource } from "typeorm";
```

### Issues Identified

1. **Cross-Module Dependencies**: Direct imports of `CustomStatusService` and `ComplianceValidationService` create tight coupling
2. **Duplicate Validation Logic**: Lines 64-86 duplicate compliance validation that already exists in the agent-context system
3. **Manual Transaction Management**: Lines 89-145 manually handle database transactions instead of using existing abstractions
4. **Service Resolution**: Direct service injection bypasses the agent-context service resolution pattern
5. **Code Duplication**: Similar submission logic exists in `ProcessDocumentHandler` (lines 204-208)

## Refactoring Opportunities

### 1. Leverage Agent-Context Services

The agent-context system already provides:
- `IEntrySubmissionService` interface (currently empty but extensible)
- `ShipmentContextWithServices._services.entrySubmissionService` 
- Existing validation utilities in `ShipmentServicesAdapter`
- `buildComplianceDetails()` method in `ShipmentResponseService`

### 2. Use Existing Submission Patterns

The `ProcessDocumentHandler` shows the correct pattern:
```typescript
const result = await this.customStatusService.processShipmentForCustomsStatus(
  shipment,
  validationResult,
  queryRunner
);
```

### 3. Consolidate Validation Logic

The agent-context system already provides:
- `context.compliance` - Pre-validated compliance data
- `context.isCompliant` - Business rule evaluation
- `context.canRush` - Rush eligibility check
- `buildComplianceDetails()` - Formatted compliance data

## Proposed Refactoring Strategy

### Phase 1: Extract Submission Service Interface

**Goal**: Create a reusable submission service interface that can be used by multiple handlers.

**Actions**:
1. Extend `IEntrySubmissionService` interface to include submission workflow methods
2. Add submission workflow method to `ShipmentServicesAdapter`
3. Update `ShipmentContextWithServices` to provide submission capabilities

**Benefits**:
- Eliminates direct `CustomStatusService` dependency
- Creates reusable submission abstraction
- Maintains transaction management within the service layer

### Phase 2: Leverage Existing Validation

**Goal**: Remove duplicate validation logic by using agent-context data.

**Actions**:
1. Remove manual compliance validation (lines 64-86)
2. Use `context.compliance` and `context.canRush` from agent-context
3. Leverage `ShipmentResponseService.buildComplianceDetails()` for formatting

**Benefits**:
- Eliminates `ComplianceValidationService` dependency
- Reduces code duplication
- Uses pre-validated context data

### Phase 3: Simplify Transaction Management

**Goal**: Move transaction management into the service layer.

**Actions**:
1. Remove manual `DataSource` and `QueryRunner` handling
2. Let the submission service handle transaction management internally
3. Focus handler on orchestration, not implementation details

**Benefits**:
- Eliminates `DataSource` dependency
- Simplifies handler logic
- Follows single responsibility principle

## Implementation Prompt for Coding Agent

**Context**: You are refactoring the `request-rush-processing.handler.ts` to improve architecture and reduce dependencies. The current implementation directly imports and uses `CustomStatusService`, `ComplianceValidationService`, and `DataSource`, creating tight coupling and code duplication.

**Your Task**: Implement the refactoring strategy outlined above. Be skeptical of the current approach and focus on:

1. **Service Abstraction**: Create proper interfaces in the agent-context system instead of direct service imports
2. **Code Reuse**: Leverage existing validation and formatting utilities from the agent-context system
3. **Dependency Reduction**: Minimize cross-module dependencies by using the established agent-context patterns
4. **Transaction Management**: Move database transaction handling into service layer abstractions

**Key Files to Modify**:
- `apps/portal-api/src/agent-context/interfaces/shipment-context-services.interface.ts` - Extend `IEntrySubmissionService`
- `apps/portal-api/src/agent-context/services/shipment-services.adapter.ts` - Add submission workflow methods
- `apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts` - Refactor to use agent-context services

**Testing Requirements**:
- Use the existing testing scripts in `apps/portal-api/src/core-agent/testing/`
- Run `rushx build` after making changes to compile templates
- Test with: `./src/core-agent/testing/run-processor-test-with-logs.sh --intent=REQUEST_RUSH_PROCESSING --verbose`
- Verify submission workflow still works correctly

**Critical Success Criteria**:
1. Handler should only import from `agent-context` system, not direct services
2. Validation logic should reuse existing agent-context data
3. Submission workflow should be abstracted behind service interfaces
4. Transaction management should be handled by service layer
5. All existing functionality must be preserved

**Be Skeptical**: Question whether each import and dependency is truly necessary. The goal is to create a clean, maintainable architecture that follows the established agent-context patterns.

## Detailed Implementation Steps

### Step 1: Extend IEntrySubmissionService Interface

Add submission workflow methods to the interface:

```typescript
export interface IEntrySubmissionService {
  /**
   * Attempts to submit a shipment for customs processing following established workflow.
   * Handles validation, timing checks, and actual submission.
   */
  attemptShipmentSubmission(
    shipment: Shipment,
    compliance: ValidateShipmentComplianceResponseDto,
    organizationId: number
  ): Promise<{
    submissionResult?: any;
    submissionError?: string;
  }>;
}
```

### Step 2: Implement in ShipmentServicesAdapter

Add the submission workflow method that encapsulates the current logic:

```typescript
async attemptShipmentSubmission(
  shipment: Shipment,
  compliance: ValidateShipmentComplianceResponseDto,
  organizationId: number
): Promise<{ submissionResult?: any; submissionError?: string }> {
  // Use existing services resolved through the adapter
  const services = await this.resolveRequestScopedServices({ id: organizationId });

  // Delegate to CustomStatusService with proper transaction management
  // This encapsulates the complex submission logic
}
```

### Step 3: Refactor Handler to Use Agent-Context

The refactored handler should look like:

```typescript
async handle(validatedIntent: ValidatedIntent, context: ShipmentContextWithServices): Promise<ResponseFragment[]> {
  // Use pre-validated context data instead of manual validation
  const canAttemptSubmission = context.canRush && !context.isSubmitted;

  // Use existing compliance formatting
  const complianceData = this.shipmentResponseService.buildComplianceDetails(context);

  // Use service abstraction for submission
  const submissionData = canAttemptSubmission
    ? await context._services.entrySubmissionService?.attemptShipmentSubmission(
        context.shipment,
        context.compliance,
        context.organization.id
      )
    : {};

  // Rest of handler logic remains the same
}
```

### Step 4: Remove Problematic Dependencies

After refactoring, the handler should only import:
- `BaseIntentHandler` and core types
- `ShipmentContext`, `ShipmentContextWithServices` from agent-context
- `ShipmentResponseService` for compliance formatting
- Email service through context._services

### Step 5: Testing Validation

Verify the refactoring works by:

1. **Build and Test**: `rushx build && ./src/core-agent/testing/run-processor-test-with-logs.sh --intent=REQUEST_RUSH_PROCESSING --verbose`

2. **Check Dependencies**: Ensure no direct imports of `CustomStatusService`, `ComplianceValidationService`, or `DataSource`

3. **Verify Functionality**: Confirm submission workflow still works for eligible shipments

4. **Test Edge Cases**: Test with shipments in different states (submitted, not ready, etc.)

## Architecture Benefits

This refactoring achieves:

1. **Separation of Concerns**: Handler focuses on orchestration, services handle implementation
2. **Dependency Inversion**: Handler depends on abstractions, not concrete services
3. **Code Reuse**: Leverages existing agent-context validation and formatting
4. **Maintainability**: Changes to submission logic only affect service layer
5. **Testability**: Service abstractions are easier to mock and test

## Risk Mitigation

**Potential Issues**:
- Service resolution timing in agent-context system
- Transaction management across service boundaries
- Maintaining backward compatibility with existing submission workflow

**Mitigation Strategies**:
- Thoroughly test service resolution patterns
- Ensure transaction boundaries are properly maintained in service layer
- Use existing `ProcessDocumentHandler` as reference for submission patterns
- Validate against existing test shipments in different states
