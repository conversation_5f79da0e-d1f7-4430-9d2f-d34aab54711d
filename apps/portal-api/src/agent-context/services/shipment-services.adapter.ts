import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource, QueryRunner } from "typeorm";
import {
  Organization,
  FIND_ORGANIZATION_RELATIONS,
  Shipment,
  ValidateShipmentComplianceResponseDto,
  Document,
  DocumentStatus,
  NonCompliantReason,
  ShipmentColumn
} from "nest-modules";
import { generateRequest } from "../../email/utils/generate-request";
import { ShipmentService } from "../../shipment/services/shipment.service";
import { ShipmentComplianceQueryService } from "../../shipment/services/shipment-compliance-query.service";
import { ComplianceValidationService } from "../../shipment/services/compliance-validation.service";
import { EntrySubmissionService } from "../../shipment/services/entry-submission.service";
import { CustomStatusService } from "../../shipment/services/custom-status.service";
import { ImporterService } from "../../importer/importer.service";
import { RNSStatusChangeEmailSender } from "../../shipment/senders/rns-status-change-email.sender";
import { RnsProofService } from "../../email/services/rns-proof-service";
import { EmailService } from "../../email/services/email.service";
import { CommercialInvoiceService } from "../../commercial-invoice/commercial-invoice.service";
import {
  IShipmentDataProvider,
  IBusinessRuleEvaluator,
  IContextFormatter,
  IEntrySubmissionService
} from "../interfaces/shipment-context-services.interface";

// Import business rule utilities and constants
import {
  isSubmittedCustomsStatus,
  isReleasedCustomsStatus,
  isSendCADReady,
  isSendRNSProofOfReleaseReady,
  isReadyToSubmit
} from "../constants/customs-definitions.constants";
import { CUSTOMS_STATUS_MESSAGES } from "../constants/customs-status-messages.constants";
import { formatComplianceResponseToStrings } from "../adapters/compliance-response.adapter";

/**
 * Interface for REQUEST-scoped services resolved via ModuleRef.
 */
interface ResolvedRequestScopedServices {
  shipmentService: ShipmentService;
  shipmentComplianceQueryService: ShipmentComplianceQueryService;
  complianceValidationService: ComplianceValidationService;
  entrySubmissionService: EntrySubmissionService;
  customStatusService: CustomStatusService;
  importerService: ImporterService;
  rnsStatusChangeEmailSender: RNSStatusChangeEmailSender;
  rnsProofService: RnsProofService;
  emailService: EmailService;
  commercialInvoiceService: CommercialInvoiceService;
}

/**
 * Adapter that implements all core interfaces and contains all the existing business logic
 * from the original ShipmentContextService. This is where the complexity lives, but it's contained.
 */
@Injectable()
export class ShipmentServicesAdapter
  implements IShipmentDataProvider, IBusinessRuleEvaluator, IContextFormatter, IEntrySubmissionService
{
  private readonly logger = new Logger(ShipmentServicesAdapter.name);

  constructor(
    private readonly moduleRef: ModuleRef,
    @InjectDataSource() private readonly dataSource: DataSource
  ) {}

  // ===== IShipmentDataProvider Implementation =====

  async fetchOrganization(organizationId: number, queryRunner?: QueryRunner): Promise<Organization> {
    this.logger.debug(`Fetching organization ${organizationId}`);

    const organization = await (queryRunner?.manager ?? this.dataSource.manager).findOne(Organization, {
      where: { id: organizationId },
      relations: FIND_ORGANIZATION_RELATIONS
    });

    if (!organization) {
      throw new NotFoundException(`Organization ${organizationId} not found`);
    }

    this.logger.debug(`Organization ${organizationId} fetched successfully`);
    return organization;
  }

  async fetchShipment(shipmentId: number, queryRunner?: QueryRunner): Promise<Shipment> {
    this.logger.debug(`Fetching shipment ${shipmentId}`);

    // Get shipment first to determine organization
    const shipment = await this.dataSource.manager.findOne(Shipment, {
      where: { id: shipmentId },
      relations: ["organization"]
    });

    if (!shipment) {
      throw new NotFoundException(`Shipment ${shipmentId} not found`);
    }

    // Fetch organization
    const organization = await this.fetchOrganization(shipment.organizationId, queryRunner);

    // Need to resolve REQUEST-scoped services first
    const services = await this.resolveRequestScopedServices(organization);

    const fullShipment = await services.shipmentService.getShipmentById(shipmentId, queryRunner);
    if (!fullShipment) {
      throw new NotFoundException(`Shipment ${shipmentId} not found`);
    }

    this.logger.debug(`Shipment ${shipmentId} fetched successfully`);
    return fullShipment;
  }

  async fetchCompliance(
    shipmentId: number,
    queryRunner?: QueryRunner
  ): Promise<ValidateShipmentComplianceResponseDto> {
    this.logger.debug(`Fetching compliance data for shipment ${shipmentId}`);

    // Get shipment first to determine organization
    const shipment = await this.dataSource.manager.findOne(Shipment, {
      where: { id: shipmentId }
    });

    if (!shipment) {
      throw new NotFoundException(`Shipment ${shipmentId} not found`);
    }

    // Fetch organization first
    const organization = await this.fetchOrganization(shipment.organizationId, queryRunner);

    // Need to resolve REQUEST-scoped services first
    const services = await this.resolveRequestScopedServices(organization);

    const compliance = await services.shipmentComplianceQueryService.getShipmentComplianceDetails(
      shipmentId,
      queryRunner
    );

    this.logger.debug(`Compliance data for shipment ${shipmentId} fetched successfully`);
    return compliance;
  }

  // ===== IBusinessRuleEvaluator Implementation =====

  canShipmentBeRushed(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): boolean {
    // Note: This needs to be resolved dynamically since ComplianceValidationService is REQUEST-scoped
    // For now, we'll implement the logic directly
    return !this.isShipmentSubmittedDirect(shipment) && isReadyToSubmit(compliance);
  }

  canGenerateCAD(customsStatus: string): boolean {
    return isSendCADReady(customsStatus);
  }

  canGenerateRNSProof(customsStatus: string): boolean {
    return isSendRNSProofOfReleaseReady(customsStatus);
  }

  isCompliant(compliance: ValidateShipmentComplianceResponseDto): boolean {
    return isReadyToSubmit(compliance);
  }

  isReleased(customsStatus: string): boolean {
    return isReleasedCustomsStatus(customsStatus);
  }

  isSubmitted(shipment: Shipment): boolean {
    return this.isShipmentSubmittedDirect(shipment);
  }

  canBeModified(shipment: Shipment): boolean {
    return !this.isShipmentSubmittedDirect(shipment);
  }

  async isEntryUploaded(shipment: Shipment): Promise<boolean> {
    try {
      // Fetch organization first
      const organization = await this.fetchOrganization(shipment.organizationId);
      const services = await this.resolveRequestScopedServices(organization);
      return services.complianceValidationService.isShipmentEntryUploaded(shipment);
    } catch (error) {
      this.logger.error(`Failed to check if entry is uploaded: ${error.message}`);
      return false;
    }
  }

  async canUpdateEntry(shipment: Shipment): Promise<boolean> {
    try {
      // Fetch organization first
      const organization = await this.fetchOrganization(shipment.organizationId);
      const services = await this.resolveRequestScopedServices(organization);
      return services.complianceValidationService.canShipmentUpdateEntry(shipment);
    } catch (error) {
      this.logger.error(`Failed to check if entry can be updated: ${error.message}`);
      return false;
    }
  }

  isAllDocsReceived(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): boolean {
    const documentDataStatus = this.buildDocumentDataStatus(shipment, compliance);

    const allCoreDocsReceived =
      documentDataStatus.hasHBLDataForSubmission &&
      documentDataStatus.hasAnEmfDataForSubmission &&
      documentDataStatus.ciReceived;

    const noDocumentFieldsMissing = !this.hasDocumentRelatedMissingFields(compliance.missingFields || []);
    const noDocumentComplianceIssues = !this.hasDocumentComplianceIssues(compliance);

    return allCoreDocsReceived && noDocumentFieldsMissing && noDocumentComplianceIssues;
  }

  determineDocumentCompleteness(shipment: Shipment): boolean {
    if (!shipment) {
      return false;
    }

    const { customsStatus } = shipment;

    // If status is pending-commercial-invoice or pending-confirmation, documents are missing
    const statusesIndicatingMissingDocuments = ["pending-commercial-invoice", "pending-confirmation"];

    const hasMissingDocuments = statusesIndicatingMissingDocuments.includes(customsStatus);

    // If customs status doesn't indicate missing documents, assume documents are complete
    // This covers cases like 'pending-arrival', 'live', 'entry-submitted', etc.
    return !hasMissingDocuments;
  }

  getRushBlockingReason(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): string {
    if (this.isShipmentSubmittedDirect(shipment)) {
      return "Shipment already submitted to customs";
    }
    if (compliance.noCommercialInvoice) {
      return "Missing commercial invoice";
    }
    if (compliance.missingFields?.length > 0) {
      return "Missing required shipment fields";
    }
    if (compliance.nonCompliantInvoices?.length > 0) {
      return "Commercial invoice compliance issues must be resolved";
    }
    return `Cannot rush shipments with status: ${shipment.customsStatus}`;
  }

  getCADBlockingReason(shipment: Shipment): string {
    if (!isSendCADReady(shipment.customsStatus)) {
      return "CAD document not available until customs entry is accepted";
    }
    return "";
  }

  getRNSBlockingReason(shipment: Shipment): string {
    if (!isSendRNSProofOfReleaseReady(shipment.customsStatus)) {
      return "Proof of release is only available once the goods have been released by customs.";
    }
    return "";
  }

  // ===== IEntrySubmissionService Implementation =====

  async attemptShipmentSubmission(
    shipment: Shipment,
    compliance: ValidateShipmentComplianceResponseDto,
    organizationId: number
  ): Promise<{
    submissionResult?: {
      liveShipmentId: number | null;
      liveEntryUploadFailedShipment: { shipmentId: number; failedReason: string } | null;
      customsStatusCheckErrorShipment: any | null;
      shipmentStatusUpdate: { shipmentId: number; newStatus: string } | null;
    };
    submissionError?: string;
  }> {
    this.logger.log(`Attempting submission workflow for shipment ${shipment.id}`);

    if (!shipment) {
      this.logger.error("No shipment found for submission");
      return { submissionError: "No shipment found for submission" };
    }

    // Check if shipment is already submitted (prevent unnecessary processing)
    const finalStatuses = ["entry-submitted", "entry-accepted", "exam", "released"];
    if (finalStatuses.includes(shipment.customsStatus)) {
      this.logger.log(
        `Submission: Skipping submission for shipment ${shipment.id} - already in final status ${shipment.customsStatus}`
      );
      return {}; // Return empty object - the template will handle the status message
    }

    try {
      // Fetch organization first for service resolution
      const organization = await this.fetchOrganization(organizationId);
      const services = await this.resolveRequestScopedServices(organization);

      // Get compliance validation (required for submission)
      const shipmentCompliances = await services.complianceValidationService.getShipmentCompliances([
        shipment
      ]);
      const validationResults = services.complianceValidationService.validateShipmentCompliances(
        shipmentCompliances,
        services.complianceValidationService.isDemoShipment(shipment)
      );

      if (validationResults.length === 0) {
        this.logger.error(`Submission: No validation results for shipment ${shipment.id}`);
        return { submissionError: "Unable to validate shipment for submission" };
      }

      const validationResult = validationResults[0];

      // Log validation outcome for debugging
      const missingFields = validationResult.missingFields || [];
      const hasInvoice = !validationResult.noCommercialInvoice;
      const hasCompliantInvoices =
        !validationResult.nonCompliantInvoices || validationResult.nonCompliantInvoices.length === 0;

      this.logger.log(
        `Submission: Shipment ${shipment.id} validation complete - hasInvoice: ${hasInvoice}, compliantInvoices: ${hasCompliantInvoices}, missingFields: [${missingFields.join(", ")}]`
      );

      // Create transaction for submission (critical for data consistency)
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      this.logger.log(`Submission: Started transaction for shipment ${shipment.id} submission`);

      try {
        // Use the established submission orchestrator
        this.logger.log(`Submission: Calling processShipmentForCustomsStatus for shipment ${shipment.id}`);

        const result = await services.customStatusService.processShipmentForCustomsStatus(
          shipment,
          validationResult,
          queryRunner
        );

        // Handle status updates (critical - sync in-memory object)
        if (result.shipmentStatusUpdate) {
          const shipmentRepository = queryRunner.manager.getRepository(Shipment);
          await shipmentRepository.update(
            { id: result.shipmentStatusUpdate.shipmentId },
            { customsStatus: result.shipmentStatusUpdate.newStatus }
          );
          shipment.customsStatus = result.shipmentStatusUpdate.newStatus;
          this.logger.log(
            `Submission: Updated shipment ${shipment.id} status to ${result.shipmentStatusUpdate.newStatus}`
          );
        }

        await queryRunner.commitTransaction();
        this.logger.log(`Submission: Transaction committed for shipment ${shipment.id}`);

        // Log submission outcome
        if (result.liveShipmentId) {
          this.logger.log(`Submission: Successfully submitted shipment ${shipment.id} to customs`);
        } else if (result.liveEntryUploadFailedShipment) {
          this.logger.error(
            `Submission: Submission failed for shipment ${shipment.id}: ${result.liveEntryUploadFailedShipment.failedReason}`
          );
        } else if (result.customsStatusCheckErrorShipment) {
          this.logger.warn(
            `Submission: Timing/validation error for shipment ${shipment.id}: ${result.customsStatusCheckErrorShipment.errorMessage}`
          );
        }

        this.logger.log(`Submission workflow completed for shipment ${shipment.id}`);
        return { submissionResult: result };
      } catch (error) {
        await queryRunner.rollbackTransaction();
        this.logger.error(`Submission: Transaction rollback for shipment ${shipment.id} - ${error.message}`);
        return { submissionError: error.message };
      } finally {
        await queryRunner.release();
        this.logger.log(`Submission: Released database connection for shipment ${shipment.id}`);
      }
    } catch (error) {
      this.logger.error(
        `Submission: Error during submission workflow for shipment ${shipment.id}: ${error.message}`
      );
      return { submissionError: error.message };
    }
  }

  // ===== IContextFormatter Implementation =====

  formatCustomsStatus(customsStatus: string): string {
    if (customsStatus && CUSTOMS_STATUS_MESSAGES[customsStatus]) {
      return CUSTOMS_STATUS_MESSAGES[customsStatus];
    }
    return customsStatus ? customsStatus.toLowerCase().replace(/-/g, " ") : "Status Unknown";
  }

  buildShipmentIdentifiers(shipment: Shipment) {
    const containers = Array.isArray(shipment.containers)
      ? shipment.containers.map((c) => c.containerNumber).filter(Boolean)
      : [];

    return {
      hblNumber: shipment.hblNumber || null,
      cargoControlNumber: shipment.cargoControlNumber || null,
      transactionNumber: shipment.transactionNumber || null,
      containerNumbers: containers,
      formattedContainers: containers.length > 0 ? containers.join(", ") : "",
      hasMultipleContainers: containers.length > 1,
      primaryContainer: containers.length > 0 ? containers[0] : null
    };
  }

  buildEtaInformation(shipment: Shipment) {
    return {
      etaPortValue: shipment.etaPort?.toLocaleDateString(),
      portName: shipment.portOfDischarge?.name,
      etaDestinationValue: shipment.etaDestination?.toLocaleDateString(),
      destinationName: shipment.placeOfDelivery?.name
    };
  }

  buildShippingInformation(shipment: Shipment) {
    const isTrackerOnline =
      shipment.trackingStatus && ["online", "tracking"].includes(shipment.trackingStatus);
    const isAir =
      shipment.modeOfTransport && ["AIR", "air", "Air"].includes(shipment.modeOfTransport as string);

    return {
      isTrackerOnline,
      shipmentStatus: shipment.status,
      isAir,
      trackingStatus: shipment.trackingStatus
    };
  }

  buildDocumentDataStatus(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto) {
    const missingFields = compliance.missingFields || [];

    const hasHBLDataForSubmission = this.hasHBLDataForSubmission(missingFields, shipment.modeOfTransport);
    const hasCompleteHBLData = this.hasCompleteHBLData(shipment, missingFields);

    const hasAnEmfDataForSubmission = this.hasAnEmfDataForSubmission(missingFields, shipment.modeOfTransport);
    const hasCompleteAnEmfData = this.hasCompleteAnEmfData(shipment, missingFields);

    const ciReceived = !compliance.noCommercialInvoice;
    const plReceived = this.hasPackingListData(shipment);

    return {
      hasHBLDataForSubmission,
      hasAnEmfDataForSubmission,
      hasCompleteHBLData,
      hasCompleteAnEmfData,
      ciReceived,
      plReceived,
      hblStatus: this.formatDocumentStatus(hasCompleteHBLData),
      anEmfStatus: this.formatDocumentStatus(hasCompleteAnEmfData),
      ciPlStatus: this.formatCIPlStatus(compliance.noCommercialInvoice, plReceived)
    };
  }

  buildMissingFieldsAnalysis(compliance: ValidateShipmentComplianceResponseDto) {
    const missingFields = compliance.missingFields || [];

    const missingIdentifiers = missingFields.filter((field) =>
      ["cargoControlNumber", "hblNumber", "transactionNumber"].includes(field)
    );

    const missingMeasurements = missingFields.filter((field) =>
      ["weight", "weightUOM", "quantity", "quantityUOM"].includes(field)
    );

    const missingTiming = missingFields.filter((field) =>
      ["etd", "etaPort", "etaDestination"].includes(field)
    );

    const missingLocations = missingFields.filter((field) =>
      ["portCode", "subLocation", "portOfLoadingId", "placeOfDelivery"].includes(field)
    );

    const ogdFilingStatus = this.getOGDFilingStatus(compliance);
    const formattedMissingFields = this.formatMissingFieldsForTemplate(missingFields, ogdFilingStatus);

    return {
      missingIdentifiers,
      missingMeasurements,
      missingTiming,
      missingLocations,
      ogdFilingStatus,
      formattedMissingFields
    };
  }

  buildTemplateContext(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto) {
    const documentDataStatus = this.buildDocumentDataStatus(shipment, compliance);
    const missingFieldsAnalysis = this.buildMissingFieldsAnalysis(compliance);

    const containers = Array.isArray(shipment.containers)
      ? shipment.containers.map((c) => c.containerNumber).filter(Boolean)
      : [];

    const identifiers = {
      ccn: shipment.cargoControlNumber || "",
      hbl: shipment.hblNumber || "",
      containers,
      formattedContainers: containers.length > 0 ? containers.join(", ") : ""
    };

    const timing = {
      etaPort: shipment.etaPort?.toISOString() || null,
      etaDestination: shipment.etaDestination?.toISOString() || null,
      releaseDate: shipment.releaseDate?.toISOString() || null,
      formattedEtaPort: this.formatDateForTemplate(shipment.etaPort),
      formattedEtaDestination: this.formatDateForTemplate(shipment.etaDestination),
      formattedReleaseDate: this.formatDateForTemplate(shipment.releaseDate)
    };

    const missingFields = {
      forPendingCommercialInvoice: this.getMissingFieldsForCIContext(missingFieldsAnalysis, compliance),
      forPendingConfirmation: this.getMissingFieldsForComplianceContext(missingFieldsAnalysis, compliance),
      formatted: missingFieldsAnalysis.formattedMissingFields
    };

    const statusContext = this.buildStatusContext(shipment, compliance, documentDataStatus);

    return {
      identifiers,
      documentStatus: {
        hblStatus: documentDataStatus.hblStatus,
        anEmfStatus: documentDataStatus.anEmfStatus,
        ciPlStatus: documentDataStatus.ciPlStatus
      },
      missingFields,
      timing,
      statusContext
    };
  }

  buildDocumentReceiptStatus(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto) {
    const documents = shipment.documents || [];

    return {
      hblReceived: this.hasDocumentReceived(documents, this.getHBLDocumentTypes(shipment.modeOfTransport)),
      anEmfReceived: this.hasDocumentReceived(
        documents,
        this.getANEMFDocumentTypes(shipment.modeOfTransport)
      ),
      ciReceived: !compliance.noCommercialInvoice,
      plReceived: this.hasDocumentReceived(documents, ["PACKING_LIST"])
    };
  }

  buildMissingFieldsStatus(compliance: ValidateShipmentComplianceResponseDto): string[] {
    const missing: string[] = [];

    if (compliance.missingFields?.includes(ShipmentColumn.cargoControlNumber)) {
      missing.push("CCN **missing**");
    }
    if (compliance.missingFields?.includes(ShipmentColumn.weight)) {
      missing.push("Weight **missing**");
    }
    if (compliance.missingFields?.includes(ShipmentColumn.hblNumber)) {
      missing.push("HBL **missing**");
    }

    if (this.getOGDFilingStatus(compliance) === "pending") {
      missing.push("OGD filing **Pending**");
    }

    return missing;
  }

  formatComplianceErrors(compliance: ValidateShipmentComplianceResponseDto): string[] {
    return formatComplianceResponseToStrings(compliance) || [];
  }

  // ===== PRIVATE HELPER METHODS (All existing business logic from original service) =====

  private async resolveRequestScopedServices(
    organization: Organization
  ): Promise<ResolvedRequestScopedServices> {
    try {
      const contextId = ContextIdFactory.create();
      this.moduleRef.registerRequestByContextId(generateRequest(null, organization), contextId);

      const [
        shipmentService,
        shipmentComplianceQueryService,
        complianceValidationService,
        entrySubmissionService,
        customStatusService,
        importerService,
        rnsStatusChangeEmailSender,
        rnsProofService,
        emailService,
        commercialInvoiceService
      ] = await Promise.all([
        this.moduleRef.resolve(ShipmentService, contextId, { strict: false }),
        this.moduleRef.resolve(ShipmentComplianceQueryService, contextId, { strict: false }),
        this.moduleRef.resolve(ComplianceValidationService, contextId, { strict: false }),
        this.moduleRef.resolve(EntrySubmissionService, contextId, { strict: false }),
        this.moduleRef.resolve(CustomStatusService, contextId, { strict: false }),
        this.moduleRef.resolve(ImporterService, contextId, { strict: false }),
        this.moduleRef.resolve(RNSStatusChangeEmailSender, contextId, { strict: false }),
        this.moduleRef.resolve(RnsProofService, contextId, { strict: false }),
        this.moduleRef.resolve(EmailService, contextId, { strict: false }),
        this.moduleRef.resolve(CommercialInvoiceService, contextId, { strict: false })
      ]);

      await new Promise((resolve) => process.nextTick(resolve));

      return {
        shipmentService,
        shipmentComplianceQueryService,
        complianceValidationService,
        entrySubmissionService,
        customStatusService,
        importerService,
        rnsStatusChangeEmailSender,
        rnsProofService,
        emailService,
        commercialInvoiceService
      };
    } catch (error) {
      this.logger.error(`Failed to resolve REQUEST-scoped services: ${error.message}`, error.stack);
      throw error;
    }
  }

  private isShipmentSubmittedDirect(shipment: Shipment): boolean {
    // Direct implementation based on customs status
    return isSubmittedCustomsStatus(shipment.customsStatus);
  }

  private hasDocumentRelatedMissingFields(missingFields: string[]): boolean {
    const documentRelatedFields = [
      "hblNumber",
      "weight",
      "weightUOM",
      "quantity",
      "quantityUOM",
      "portCode",
      "portOfLoadingId",
      "cargoControlNumber",
      "etd",
      "etaPort",
      "etaDestination",
      "subLocation",
      "transactionNumber",
      "volume",
      "volumeUOM",
      "vessel",
      "voyageNumber"
    ];

    return missingFields.some((field) => documentRelatedFields.includes(field));
  }

  private hasDocumentComplianceIssues(compliance: ValidateShipmentComplianceResponseDto): boolean {
    const hasInvoiceIssues = compliance.nonCompliantInvoices?.length > 0;
    const hasOGDIssues = this.getOGDFilingStatus(compliance) === "pending";
    return hasInvoiceIssues || hasOGDIssues;
  }

  private hasHBLDataForSubmission(missingFields: string[], modeOfTransport: string): boolean {
    const requiredHBLFields = this.getRequiredHBLFields(modeOfTransport);
    return !requiredHBLFields.some((field) => missingFields.includes(field));
  }

  private hasAnEmfDataForSubmission(missingFields: string[], modeOfTransport: string): boolean {
    const requiredAnEmfFields = this.getRequiredAnEmfFields(modeOfTransport);
    return !requiredAnEmfFields.some((field) => missingFields.includes(field));
  }

  private hasCompleteHBLData(shipment: Shipment, missingFields: string[]): boolean {
    const allHBLFields = [
      ...this.getRequiredHBLFields(shipment.modeOfTransport),
      "hblNumber",
      ...(shipment.modeOfTransport?.toLowerCase().includes("ocean") ? ["vesselName", "voyageNumber"] : [])
    ];
    return !allHBLFields.some((field) => missingFields.includes(field));
  }

  private hasCompleteAnEmfData(shipment: Shipment, missingFields: string[]): boolean {
    const allAnEmfFields = this.getRequiredAnEmfFields(shipment.modeOfTransport);
    return !allAnEmfFields.some((field) => missingFields.includes(field));
  }

  private getRequiredHBLFields(modeOfTransport: string): string[] {
    const baseFields = ["weight", "weightUOM"];
    switch (modeOfTransport?.toUpperCase()) {
      case "OCEAN_FCL":
      case "OCEAN_LCL":
        return [...baseFields, "portCode", "portOfLoadingId"];
      case "AIR":
        return [...baseFields, "portCode", "portOfLoadingId"];
      case "LAND":
        return [...baseFields];
      default:
        return [...baseFields, "portCode"];
    }
  }

  private getRequiredAnEmfFields(modeOfTransport: string): string[] {
    const baseFields = ["cargoControlNumber", "etd", "etaDestination"];
    switch (modeOfTransport?.toUpperCase()) {
      case "OCEAN_FCL":
      case "OCEAN_LCL":
        return [...baseFields, "subLocation"];
      case "AIR":
        return [...baseFields, "subLocation"];
      case "LAND":
        return [...baseFields];
      default:
        return baseFields;
    }
  }

  private hasPackingListData(shipment: Shipment): boolean {
    return !!(shipment.quantity && shipment.quantityUOM);
  }

  private formatDocumentStatus(hasData: boolean): "Received" | "Missing" {
    return hasData ? "Received" : "Missing";
  }

  private formatCIPlStatus(noCommercialInvoice: boolean, hasPackingList: boolean): "Received" | "Missing" {
    return !noCommercialInvoice && hasPackingList ? "Received" : "Missing";
  }

  private getOGDFilingStatus(
    compliance: ValidateShipmentComplianceResponseDto
  ): "pending" | "complete" | "not-required" {
    const hasOGDFilingIssue = compliance.nonCompliantInvoices?.some((invoice) =>
      invoice.nonCompliantLines?.some((line) =>
        line.nonCompliantRecords?.some((record) => record.reason === NonCompliantReason.MISSING_OGD_FILING)
      )
    );

    if (hasOGDFilingIssue) {
      return "pending";
    }

    const hasControlledGoods = compliance.nonCompliantInvoices?.some((invoice) =>
      invoice.nonCompliantLines?.some((line) =>
        line.nonCompliantRecords?.some((record) => record.reason && record.reason.toString().includes("OGD"))
      )
    );

    return hasControlledGoods ? "complete" : "not-required";
  }

  private formatMissingFieldsForTemplate(
    missingFields: string[],
    ogdFilingStatus: "pending" | "complete" | "not-required"
  ): string[] {
    const formatted: string[] = [];

    const fieldMapping: Record<string, string> = {
      cargoControlNumber: "CCN **missing**",
      hblNumber: "HBL **missing**",
      weight: "Weight **missing**",
      weightUOM: "Weight unit **missing**",
      quantity: "Quantity **missing**",
      quantityUOM: "Quantity unit **missing**",
      etd: "ETD **missing**",
      etaPort: "ETA Port **missing**",
      etaDestination: "ETA Destination **missing**",
      portCode: "Port code **missing**",
      subLocation: "Sub-location **missing**",
      portOfLoadingId: "Port of loading **missing**",
      transactionNumber: "Transaction number **missing**"
    };

    missingFields.forEach((field) => {
      if (fieldMapping[field]) {
        formatted.push(fieldMapping[field]);
      }
    });

    if (ogdFilingStatus === "pending") {
      formatted.push("OGD filing **Pending**");
    }

    return formatted;
  }

  private getMissingFieldsForCIContext(
    missingFieldsAnalysis: {
      missingMeasurements: string[];
      ogdFilingStatus: string;
    },
    compliance: ValidateShipmentComplianceResponseDto
  ): string[] {
    const ciRelatedFields: string[] = [];

    if (compliance.noCommercialInvoice) {
      ciRelatedFields.push("Commercial invoice required");
    }

    if (missingFieldsAnalysis.ogdFilingStatus === "pending") {
      ciRelatedFields.push("OGD filing pending");
    }

    ciRelatedFields.push(
      ...missingFieldsAnalysis.missingMeasurements.map((field: string) =>
        this.formatMissingFieldForTemplate(field)
      )
    );

    return ciRelatedFields;
  }

  private getMissingFieldsForComplianceContext(
    missingFieldsAnalysis: {
      missingIdentifiers: string[];
      missingMeasurements: string[];
      missingTiming: string[];
      missingLocations: string[];
    },
    compliance: ValidateShipmentComplianceResponseDto
  ): string[] {
    const complianceFields: string[] = [];

    complianceFields.push(
      ...missingFieldsAnalysis.missingIdentifiers.map((field: string) =>
        this.formatMissingFieldForTemplate(field)
      )
    );
    complianceFields.push(
      ...missingFieldsAnalysis.missingMeasurements.map((field: string) =>
        this.formatMissingFieldForTemplate(field)
      )
    );
    complianceFields.push(
      ...missingFieldsAnalysis.missingTiming.map((field: string) => this.formatMissingFieldForTemplate(field))
    );
    complianceFields.push(
      ...missingFieldsAnalysis.missingLocations.map((field: string) =>
        this.formatMissingFieldForTemplate(field)
      )
    );

    return complianceFields;
  }

  private formatDateForTemplate(date: Date | null | undefined): string {
    if (!date) return "TBD";

    try {
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    } catch {
      return "TBD";
    }
  }

  private formatMissingFieldForTemplate(fieldName: string): string {
    const fieldMapping: Record<string, string> = {
      cargoControlNumber: "CCN missing",
      hblNumber: "HBL missing",
      weight: "Weight missing",
      weightUOM: "Weight unit missing",
      quantity: "Quantity missing",
      quantityUOM: "Quantity unit missing",
      etd: "ETD missing",
      etaPort: "ETA Port missing",
      etaDestination: "ETA Destination missing",
      portCode: "Port code missing",
      subLocation: "Sub-location missing",
      portOfLoadingId: "Port of loading missing",
      transactionNumber: "Transaction number missing"
    };

    return fieldMapping[fieldName] || `${fieldName} missing`;
  }

  private buildStatusContext(
    shipment: Shipment,
    compliance: ValidateShipmentComplianceResponseDto,
    documentDataStatus: {
      hasHBLDataForSubmission: boolean;
      hasAnEmfDataForSubmission: boolean;
      ciReceived: boolean;
      plReceived: boolean;
    }
  ): { primaryMessage: string; secondaryDetails: string[]; actionRequired: boolean } {
    const status = shipment.customsStatus?.toLowerCase() || "unknown";
    const secondaryDetails: string[] = [];
    let primaryMessage = "";
    let actionRequired = false;

    if (status.includes("pending")) {
      primaryMessage = "Shipment awaiting processing";
      actionRequired = true;

      if (!documentDataStatus.ciReceived) {
        secondaryDetails.push("Commercial invoice required");
      }
      if (compliance.missingFields?.length > 0) {
        secondaryDetails.push("Additional shipment details needed");
      }
    } else if (status.includes("accepted") || status.includes("released")) {
      primaryMessage = "Shipment cleared for release";
      actionRequired = false;

      if (shipment.releaseDate) {
        secondaryDetails.push(`Released on ${this.formatDateForTemplate(shipment.releaseDate)}`);
      }
    } else if (status.includes("exam")) {
      primaryMessage = "Shipment selected for examination";
      actionRequired = false;
      secondaryDetails.push("Please await further instructions");
    } else {
      primaryMessage = `Status: ${this.formatCustomsStatus(shipment.customsStatus)}`;
      actionRequired = compliance.missingFields?.length > 0 || compliance.noCommercialInvoice;
    }

    return {
      primaryMessage,
      secondaryDetails,
      actionRequired
    };
  }

  private getHBLDocumentTypes(modeOfTransport: string): string[] {
    switch (modeOfTransport?.toUpperCase()) {
      case "OCEAN_FCL":
      case "OCEAN_LCL":
        return ["HOUSE_OCEAN_BILL_OF_LADING"];
      case "AIR":
        return ["AIR_WAYBILL"];
      case "LAND":
        return ["ROAD_BILL_OF_LADING"];
      default:
        return ["HOUSE_OCEAN_BILL_OF_LADING", "AIR_WAYBILL", "ROAD_BILL_OF_LADING"];
    }
  }

  private getANEMFDocumentTypes(modeOfTransport: string): string[] {
    switch (modeOfTransport?.toUpperCase()) {
      case "OCEAN_FCL":
      case "OCEAN_LCL":
        return ["OCEAN_ARRIVAL_NOTICE", "OCEAN_E_MANIFEST"];
      case "AIR":
        return ["AIR_ARRIVAL_NOTICE", "AIR_E_MANIFEST"];
      case "LAND":
        return ["ROAD_ARRIVAL_NOTICE"];
      default:
        return [
          "OCEAN_ARRIVAL_NOTICE",
          "OCEAN_E_MANIFEST",
          "AIR_ARRIVAL_NOTICE",
          "AIR_E_MANIFEST",
          "ROAD_ARRIVAL_NOTICE"
        ];
    }
  }

  private hasDocumentReceived(documents: Document[], documentTypes: string[]): boolean {
    return documents.some(
      (doc) =>
        documentTypes.includes(doc.name) &&
        [DocumentStatus.EXTRACTED, DocumentStatus.AGGREGATED].includes(doc.status as DocumentStatus) &&
        !doc.isShipmentMismatch
    );
  }
}
