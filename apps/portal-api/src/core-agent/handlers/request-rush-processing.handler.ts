import { Injectable, Inject } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContextWithServices } from "../../agent-context";
import { ShipmentResponseService } from "../services/shipment-response.service";

/**
 * <PERSON><PERSON> requests for expedited/rush processing of shipments.
 * Evaluates business rules, attempts submission when appropriate, and sends backoffice alerts.
 * Follows the established submission workflow pattern for consistency.
 */
@Injectable()
export class RequestRushProcessingHandler extends BaseIntentHandler {
  constructor(
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_RUSH_PROCESSING" as const,
    description: "User is requesting expedited or rush processing for their shipment",
    examples: [
      "Can you rush this shipment?",
      "We need this expedited",
      "Please prioritize this load",
      "This is urgent, can you speed it up?",
      "Rush processing needed"
    ],
    keywords: ["rush", "urgent", "expedite", "prioritize", "speed", "asap", "quickly", "fast"]
  };

  /**
   * Attempts to submit the shipment for rush processing using agent-context services.
   * Returns submission results for template context.
   */
  private async attemptSubmission(context: ShipmentContextWithServices): Promise<any> {
    const shipment = context.shipment;
    if (!shipment) {
      this.logger.error("No shipment found in context for submission");
      return { submissionError: "No shipment found for submission" };
    }

    // Use pre-validated context data to determine if submission should be attempted
    const canAttemptSubmission = context.canRush && !context.isSubmitted;

    if (!canAttemptSubmission) {
      this.logger.log(
        `Rush processing: Skipping submission for shipment ${shipment.id} - canRush: ${context.canRush}, isSubmitted: ${context.isSubmitted}`
      );
      return {}; // Return empty object - the template will handle the status message
    }

    // Use the agent-context service abstraction for submission
    if (!context._services.entrySubmissionService) {
      this.logger.error("EntrySubmissionService not available in context");
      return { submissionError: "Submission service not available" };
    }

    try {
      this.logger.log(`Rush processing: Attempting submission for shipment ${shipment.id}`);

      const submissionResult = await context._services.entrySubmissionService.attemptShipmentSubmission(
        shipment,
        context.compliance,
        context.organization.id
      );

      this.logger.log(`Rush processing submission completed for shipment ${shipment.id}`);
      return submissionResult;
    } catch (error) {
      this.logger.error(
        `Rush processing: Error during submission workflow for shipment ${shipment.id}: ${error.message}`
      );
      return { submissionError: error.message };
    }
  }

  async handle(
    validatedIntent: ValidatedIntent,
    context: ShipmentContextWithServices
  ): Promise<ResponseFragment[]> {
    const fragments: ResponseFragment[] = [];
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(`Handling REQUEST_RUSH_PROCESSING for shipment ${context.shipment?.id || "N/A"}`);

    if (instructions.length === 0) {
      this.logger.error("Cannot process rush processing request: no instructions provided");
      throw new Error("No instructions provided for rush processing request");
    }

    try {
      // Collect data from helper methods (no context mutation)
      const alertData = await this.sendBackofficeAlert(
        "Rush Processing Request",
        context.shipment?.id || 0,
        instructions,
        context._services.emailService,
        context.organization.id
      );
      const complianceData = this.shipmentResponseService.buildComplianceDetails(context);

      // Attempt submission workflow for rush processing
      this.logger.log(
        `Attempting submission workflow for shipment ${context.shipment?.id} with status ${context.shipment?.customsStatus}`
      );
      const submissionData = await this.attemptSubmission(context);

      // Rush processing response fragment (priority 1)
      fragments.push({
        template: "core-agent/fragments/document-requests/rush-processing-response",
        priority: 1,
        fragmentContext: {
          ...alertData, // { backofficeAlerts: { rushProcessingSent: true } }
          ...complianceData, // { complianceDetails: {...}, missingFieldsFormatted: "..." }
          ...submissionData, // { submissionResult: {...} } or { submissionError: "..." }
          directlyAsked: { rush_processing: true }
        }
      });

      // Shipment details fragment (priority 2)
      fragments.push({
        template: "core-agent/fragments/details",
        priority: 2
      });
    } catch (error) {
      this.logger.error(
        `Failed to process rush processing request for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }

    return fragments;
  }
}
